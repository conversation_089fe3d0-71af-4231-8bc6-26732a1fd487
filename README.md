# Google Form Auto-Filler Bot

This Python bot automatically fills a Google Form multiple times with incremental email addresses.

## Features

- Automatically fills Google Forms with specified name and incremental email addresses
- Configurable number of submissions
- Automatic ChromeDriver management
- Detailed logging of the process
- Error handling and retry logic

## Prerequisites

1. **Python 3.7+** - Make sure Python is installed on your system
2. **Google Chrome** - The bot uses Chrome browser for automation
3. **Internet connection** - Required to access the form and download ChromeDriver

## Installation

1. **Clone or download** this repository to your local machine

2. **Run the setup script** to install dependencies:
   ```bash
   python setup.py
   ```

   Or manually install requirements:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

Before running the bot, you may want to modify the configuration in `form_filler_bot.py`:

```python
# Configuration (in the main() function)
FORM_URL = "https://forms.gle/2cHuJVWGHVi3cDhA8"  # Your form URL
NAME = "Parth Thapa"                              # Name to fill in the form
BASE_EMAIL = "parththapa"                         # Base email (without number)
NUM_SUBMISSIONS = 5                               # Number of submissions
START_NUMBER = 1                                  # Starting number for emails
HEADLESS = False                                  # Set True to run without browser window
```

## Usage

1. **Run the bot**:
   ```bash
   python form_filler_bot.py
   ```

2. **The bot will**:
   - Open Chrome browser
   - Navigate to the form
   - Fill the form with "Parth Thapa" and incremental emails:
     - <EMAIL>
     - <EMAIL>
     - <EMAIL>
     - etc.
   - Submit each form
   - Provide detailed logging of the process

## Customization

### Form Field Selectors

The bot uses common selectors to find form fields. If the bot can't find the fields in your specific form, you may need to:

1. **Inspect the form** in your browser (F12 → Elements tab)
2. **Find the correct selectors** for name and email fields
3. **Update the selectors** in the `fill_form()` method

### Email Pattern

To change the email pattern, modify the email generation line:
```python
email = f"{base_email}{email_number}@gmail.com"
```

For example, to use a different domain:
```python
email = f"{base_email}{email_number}@example.com"
```

### Submission Delay

To change the delay between submissions, modify:
```python
time.sleep(2)  # Delay in seconds
```

## Troubleshooting

### Common Issues

1. **"Chrome not found"**
   - Install Google Chrome browser
   - Make sure it's in the default installation location

2. **"Could not find name/email field"**
   - The form structure might be different
   - Inspect the form and update the field selectors

3. **"Could not find submit button"**
   - The submit button selector might need updating
   - Check the form's submit button structure

4. **Form submission fails**
   - The form might have additional required fields
   - Check if there are captchas or other validation

### Debug Mode

To see what's happening, you can:
1. Set `HEADLESS = False` to see the browser
2. Add more `time.sleep()` calls to slow down the process
3. Check the console output for detailed logs

## Important Notes

- **Use responsibly**: Don't spam forms or violate terms of service
- **Rate limiting**: The bot includes delays to avoid being flagged
- **Form changes**: If the form structure changes, you may need to update selectors
- **Testing**: Always test with a small number of submissions first

## Legal Disclaimer

This tool is for educational and legitimate testing purposes only. Users are responsible for ensuring they have permission to submit to the target form and comply with all applicable terms of service and laws.
