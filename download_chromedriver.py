#!/usr/bin/env python3
"""
ChromeDriver Download Helper
Downloads the correct ChromeDriver version for your Chrome browser
"""

import os
import sys
import zipfile
import requests
import subprocess
import platform
from pathlib import Path

def get_chrome_version():
    """Get the installed Chrome version"""
    try:
        if platform.system() == "Windows":
            # Try different methods to get Chrome version on Windows
            import winreg
            try:
                # Method 1: Registry
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                # Method 2: Command line
                result = subprocess.run([
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe", "--version"
                ], capture_output=True, text=True)
                if result.returncode == 0:
                    return result.stdout.strip().split()[-1]
        
        elif platform.system() == "Darwin":  # macOS
            result = subprocess.run([
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"
            ], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split()[-1]
        
        else:  # Linux
            result = subprocess.run(["google-chrome", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split()[-1]
    
    except Exception as e:
        print(f"Could not detect Chrome version: {e}")
    
    return None

def get_chromedriver_url(chrome_version):
    """Get the ChromeDriver download URL for the Chrome version"""
    # Extract major version
    major_version = chrome_version.split('.')[0]
    
    # ChromeDriver URL pattern for newer versions
    base_url = "https://storage.googleapis.com/chrome-for-testing-public"
    
    # Determine platform
    system = platform.system().lower()
    machine = platform.machine().lower()
    
    if system == "windows":
        if "64" in machine or "amd64" in machine:
            platform_name = "win64"
        else:
            platform_name = "win32"
    elif system == "darwin":
        if "arm" in machine:
            platform_name = "mac-arm64"
        else:
            platform_name = "mac-x64"
    else:  # Linux
        platform_name = "linux64"
    
    # Try to get the exact version
    try:
        # Get available versions
        versions_url = f"{base_url}/LATEST_RELEASE_{major_version}"
        response = requests.get(versions_url)
        if response.status_code == 200:
            exact_version = response.text.strip()
            download_url = f"{base_url}/{exact_version}/{platform_name}/chromedriver-{platform_name}.zip"
            return download_url, exact_version
    except:
        pass
    
    # Fallback: use the Chrome version as-is
    download_url = f"{base_url}/{chrome_version}/{platform_name}/chromedriver-{platform_name}.zip"
    return download_url, chrome_version

def download_chromedriver(url, version):
    """Download and extract ChromeDriver"""
    print(f"Downloading ChromeDriver {version}...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Save to current directory
        zip_path = "chromedriver.zip"
        with open(zip_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("Extracting ChromeDriver...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        # Remove zip file
        os.remove(zip_path)
        
        # Find the extracted chromedriver executable
        chromedriver_path = None
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.startswith("chromedriver") and (file.endswith(".exe") or "chromedriver" == file):
                    chromedriver_path = os.path.join(root, file)
                    break
            if chromedriver_path:
                break
        
        if chromedriver_path:
            # Move to current directory if it's in a subdirectory
            final_path = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
            if chromedriver_path != final_path:
                os.rename(chromedriver_path, final_path)
                # Clean up extracted directory
                import shutil
                for item in os.listdir("."):
                    if os.path.isdir(item) and "chromedriver" in item:
                        shutil.rmtree(item)
            
            # Make executable on Unix systems
            if platform.system() != "Windows":
                os.chmod(final_path, 0o755)
            
            print(f"✓ ChromeDriver downloaded successfully: {final_path}")
            return final_path
        else:
            print("✗ Could not find ChromeDriver executable in downloaded archive")
            return None
            
    except Exception as e:
        print(f"✗ Failed to download ChromeDriver: {e}")
        return None

def main():
    """Main function"""
    print("=== ChromeDriver Download Helper ===\n")
    
    # Get Chrome version
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("Could not detect Chrome version. Please install Google Chrome first.")
        sys.exit(1)
    
    print(f"Detected Chrome version: {chrome_version}")
    
    # Get download URL
    url, driver_version = get_chromedriver_url(chrome_version)
    print(f"ChromeDriver version to download: {driver_version}")
    
    # Download ChromeDriver
    driver_path = download_chromedriver(url, driver_version)
    
    if driver_path:
        print(f"\n✓ Setup complete!")
        print(f"ChromeDriver is ready at: {os.path.abspath(driver_path)}")
        print("\nYou can now run the form filler:")
        print("python form_filler_simple.py")
    else:
        print("\n✗ Setup failed. Please download ChromeDriver manually from:")
        print("https://chromedriver.chromium.org/")

if __name__ == "__main__":
    main()
