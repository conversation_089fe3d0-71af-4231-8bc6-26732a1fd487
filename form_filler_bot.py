#!/usr/bin/env python3
"""
Google Form Auto-Filler Bot
Automatically fills a Google Form multiple times with incremental email addresses
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleFormFiller:
    def __init__(self, form_url, headless=False):
        """
        Initialize the form filler bot
        
        Args:
            form_url (str): The Google Form URL
            headless (bool): Whether to run browser in headless mode
        """
        self.form_url = form_url
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            # Try different approaches to get the right ChromeDriver
            service = None

            # First, try with webdriver-manager
            try:
                logger.info("Attempting to use webdriver-manager...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e1:
                logger.warning(f"webdriver-manager failed: {e1}")

                # Fallback: try without service (assumes ChromeDriver is in PATH)
                try:
                    logger.info("Trying without service (ChromeDriver in PATH)...")
                    self.driver = webdriver.Chrome(options=chrome_options)
                except Exception as e2:
                    logger.error(f"Both methods failed. webdriver-manager: {e1}, PATH: {e2}")
                    raise Exception(f"Could not initialize ChromeDriver. Please ensure Chrome and ChromeDriver are properly installed.")

            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome WebDriver initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise
    
    def fill_form(self, name, email, submission_number):
        """
        Fill the Google Form with provided data
        
        Args:
            name (str): Name to fill in the form
            email (str): Email to fill in the form
            submission_number (int): Current submission number for logging
        """
        try:
            logger.info(f"Submission #{submission_number}: Filling form for {email}")
            
            # Navigate to the form
            self.driver.get(self.form_url)
            time.sleep(2)  # Wait for page to load
            
            # Wait for form to be ready
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))
            
            # Find and fill form fields
            # Note: You'll need to inspect the actual form to get the correct selectors
            
            # Common selectors for Google Forms (you may need to adjust these)
            name_selectors = [
                "input[aria-label*='Name']",
                "input[aria-label*='name']",
                "input[data-initial-value='']",
                "input[type='text']"
            ]
            
            email_selectors = [
                "input[aria-label*='Email']",
                "input[aria-label*='email']",
                "input[type='email']"
            ]
            
            # Try to find and fill name field
            name_filled = False
            for selector in name_selectors:
                try:
                    name_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    name_field.clear()
                    name_field.send_keys(name)
                    logger.info(f"Name field filled with: {name}")
                    name_filled = True
                    break
                except NoSuchElementException:
                    continue
            
            if not name_filled:
                logger.warning("Could not find name field")
            
            # Try to find and fill email field
            email_filled = False
            for selector in email_selectors:
                try:
                    email_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    email_field.clear()
                    email_field.send_keys(email)
                    logger.info(f"Email field filled with: {email}")
                    email_filled = True
                    break
                except NoSuchElementException:
                    continue
            
            if not email_filled:
                logger.warning("Could not find email field")
            
            # Wait a moment before submitting
            time.sleep(1)
            
            # Find and click submit button
            submit_selectors = [
                "div[role='button'][aria-label*='Submit']",
                "div[role='button']span:contains('Submit')",
                "input[type='submit']",
                "button[type='submit']"
            ]
            
            submitted = False
            for selector in submit_selectors:
                try:
                    if 'contains' in selector:
                        # Use XPath for text content search
                        submit_button = self.driver.find_element(By.XPATH, "//div[@role='button']//span[contains(text(), 'Submit')]")
                    else:
                        submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    submit_button.click()
                    logger.info(f"Form submitted successfully for {email}")
                    submitted = True
                    break
                except NoSuchElementException:
                    continue
            
            if not submitted:
                logger.error("Could not find submit button")
                return False
            
            # Wait for submission confirmation
            time.sleep(3)
            
            return True
            
        except TimeoutException:
            logger.error(f"Timeout while filling form for {email}")
            return False
        except Exception as e:
            logger.error(f"Error filling form for {email}: {e}")
            return False
    
    def run_multiple_submissions(self, base_name, base_email, num_submissions, start_number=1):
        """
        Run multiple form submissions with incremental emails
        
        Args:
            base_name (str): Base name to use (e.g., "Parth Thapa")
            base_email (str): Base email without number (e.g., "parththapa")
            num_submissions (int): Number of submissions to make
            start_number (int): Starting number for email increment
        """
        successful_submissions = 0
        failed_submissions = 0
        
        for i in range(num_submissions):
            email_number = start_number + i
            email = f"{base_email}{email_number}@gmail.com"
            
            success = self.fill_form(base_name, email, i + 1)
            
            if success:
                successful_submissions += 1
                logger.info(f"✓ Submission {i + 1} completed successfully")
            else:
                failed_submissions += 1
                logger.error(f"✗ Submission {i + 1} failed")
            
            # Add delay between submissions to avoid being flagged
            if i < num_submissions - 1:  # Don't wait after the last submission
                time.sleep(2)
        
        logger.info(f"\n=== SUMMARY ===")
        logger.info(f"Total submissions attempted: {num_submissions}")
        logger.info(f"Successful submissions: {successful_submissions}")
        logger.info(f"Failed submissions: {failed_submissions}")
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def main():
    """Main function to run the form filler"""
    # Configuration
    FORM_URL = "https://forms.gle/2cHuJVWGHVi3cDhA8"
    NAME = "Parth Thapa"
    BASE_EMAIL = "parththapa"  # <NAME_EMAIL>, <EMAIL>, etc.
    NUM_SUBMISSIONS = 15  # Number of times to fill the form
    START_NUMBER = 1  # Starting number for email increment
    HEADLESS = False  # Set to True to run without opening browser window
    
    # Create and run the form filler
    form_filler = GoogleFormFiller(FORM_URL, headless=HEADLESS)
    
    try:
        form_filler.run_multiple_submissions(
            base_name=NAME,
            base_email=BASE_EMAIL,
            num_submissions=NUM_SUBMISSIONS,
            start_number=START_NUMBER
        )
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
    finally:
        form_filler.close()

if __name__ == "__main__":
    main()
