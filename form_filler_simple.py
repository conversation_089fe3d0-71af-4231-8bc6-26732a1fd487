#!/usr/bin/env python3
"""
Simple Google Form Auto-Filler Bot (without webdriver-manager)
Automatically fills a Google Form multiple times with incremental email addresses
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleFormFiller:
    def __init__(self, form_url, headless=False, manual_login=True):
        """
        Initialize the form filler bot

        Args:
            form_url (str): The Google Form URL
            headless (bool): Whether to run browser in headless mode
            manual_login (bool): Whether to wait for manual login
        """
        self.form_url = form_url
        self.driver = None
        self.wait = None
        self.manual_login = manual_login
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            # Simple approach - assumes ChromeDriver is in PATH or same directory
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            logger.error("Please ensure ChromeDriver is installed and in your PATH")
            logger.error("Download from: https://chromedriver.chromium.org/")
            raise

    def handle_google_login(self):
        """Handle Google login process"""
        if not self.manual_login:
            return True

        try:
            logger.info("Opening Google login page...")
            self.driver.get("https://accounts.google.com/signin")

            logger.info("=" * 60)
            logger.info("PLEASE SIGN IN TO YOUR GOOGLE ACCOUNT")
            logger.info("=" * 60)
            logger.info("1. The browser window should now be open")
            logger.info("2. Please sign in to your Google account")
            logger.info("3. Complete any 2FA if required")
            logger.info("4. Once signed in, press ENTER here to continue...")
            logger.info("=" * 60)

            # Wait for user to complete login
            input("Press ENTER after you have successfully signed in to Google: ")

            # Verify login by checking if we can access Google services
            logger.info("Verifying login status...")
            self.driver.get("https://myaccount.google.com/")
            time.sleep(3)

            # Check if we're logged in (look for account elements)
            try:
                # Look for elements that indicate successful login
                self.wait.until(EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-ogsr-up]")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[aria-label*='Google Account']")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".gb_A"))
                ))
                logger.info("✓ Google login verified successfully!")
                return True
            except TimeoutException:
                logger.warning("Could not verify login status, but continuing anyway...")
                return True

        except Exception as e:
            logger.error(f"Error during login process: {e}")
            logger.info("Continuing anyway - the form might still work...")
            return True
    
    def fill_form(self, name, email, submission_number):
        """
        Fill the Google Form with provided data

        Args:
            name (str): Name to fill in the form
            email (str): Email to fill in the form
            submission_number (int): Current submission number for logging
        """
        try:
            logger.info(f"Submission #{submission_number}: Filling form for {email}")

            # Navigate to the form
            self.driver.get(self.form_url)
            time.sleep(3)  # Wait for page to load

            # Wait for form to be ready
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))

            # Fill the comprehensive form
            self.fill_comprehensive_form(name, email)

            # Wait a moment before submitting
            time.sleep(2)

            # Find and click submit button
            if self.submit_form():
                logger.info(f"Form submitted successfully for {email}")
                time.sleep(3)
                return True
            else:
                logger.error("Failed to submit form")
                return False

        except TimeoutException:
            logger.error(f"Timeout while filling form for {email}")
            return False
        except Exception as e:
            logger.error(f"Error filling form for {email}: {e}")
            return False

    def fill_comprehensive_form(self, name, email):
        """Fill all form fields with realistic data"""
        import random

        # 1. Fill Name field
        try:
            name_field = self.driver.find_element(By.XPATH, "//input[@aria-label='Name']")
            name_field.clear()
            name_field.send_keys(name)
            logger.info(f"Name filled: {name}")
        except:
            logger.warning("Could not find name field")

        # 2. Fill Age - randomly select from available options
        try:
            age_options = ["16-18", "18-22", "23-26", "Above 26"]
            selected_age = random.choice(age_options)
            age_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_age}')]/preceding-sibling::div//input[@type='radio']")
            age_radio.click()
            logger.info(f"Age selected: {selected_age}")
        except Exception as e:
            logger.warning(f"Could not select age: {e}")

        # 3. Fill Gender - randomly select
        try:
            genders = ["Male", "Female", "Non-Binary"]
            selected_gender = random.choice(genders)
            gender_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_gender}')]/preceding-sibling::div//input[@type='radio']")
            gender_radio.click()
            logger.info(f"Gender selected: {selected_gender}")
        except Exception as e:
            logger.warning(f"Could not select gender: {e}")

        # 4. Fill Education Level - randomly select
        try:
            education_levels = [
                "Currently in High School",
                "High School Graduate",
                "Currently pursuing a bachelor's degree",
                "Currently pursuing a master's degree",
                "Completed bachelor's degree",
                "Completed master's degree"
            ]
            selected_education = random.choice(education_levels)
            education_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_education}')]/preceding-sibling::div//input[@type='radio']")
            education_radio.click()
            logger.info(f"Education selected: {selected_education}")
        except Exception as e:
            logger.warning(f"Could not select education: {e}")

        # 5. Fill Major - randomly select
        try:
            majors = ["Bsc.IT", "BBA", "MSc.IT", "MBA"]
            selected_major = random.choice(majors)
            major_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_major}')]/preceding-sibling::div//input[@type='radio']")
            major_radio.click()
            logger.info(f"Major selected: {selected_major}")
        except Exception as e:
            logger.warning(f"Could not select major: {e}")

        time.sleep(1)  # Brief pause between sections

        # 6. Digital health app knowledge
        try:
            knowledge_options = ["Yes", "No"]
            selected_knowledge = random.choice(knowledge_options)
            knowledge_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_knowledge}') and ancestor::div[contains(@aria-label, 'digital health apps')]]/preceding-sibling::div//input[@type='radio']")
            knowledge_radio.click()
            logger.info(f"Digital health app knowledge: {selected_knowledge}")
        except Exception as e:
            logger.warning(f"Could not select digital health app knowledge: {e}")

        # 7. Have you used digital health app
        try:
            usage_options = ["Yes", "No"]
            selected_usage = random.choice(usage_options)
            usage_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_usage}') and ancestor::div[contains(@aria-label, 'Have you ever used')]]/preceding-sibling::div//input[@type='radio']")
            usage_radio.click()
            logger.info(f"Digital health app usage: {selected_usage}")
        except Exception as e:
            logger.warning(f"Could not select digital health app usage: {e}")

        # 8. How often do you use them (if applicable)
        try:
            frequency_options = ["Daily", "A few times a week", "A few times a month", "Rarely"]
            selected_frequency = random.choice(frequency_options)
            frequency_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_frequency}')]/preceding-sibling::div//input[@type='radio']")
            frequency_radio.click()
            logger.info(f"Usage frequency: {selected_frequency}")
        except Exception as e:
            logger.warning(f"Could not select usage frequency: {e}")

        # 9. Which apps have you used (checkboxes - select multiple randomly)
        try:
            app_types = [
                "Fitness/Workout Apps",
                "Period/Menstruation Apps",
                "Meditation/Mental health apps",
                "Diet and Calorie tracking apps",
                "Telemedicine/Doctor consultation apps",
                "Step/Activity tracking apps"
            ]
            # Select 2-4 random app types
            num_to_select = random.randint(2, 4)
            selected_apps = random.sample(app_types, num_to_select)

            for app_type in selected_apps:
                try:
                    checkbox = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{app_type}')]/preceding-sibling::div//input[@type='checkbox']")
                    checkbox.click()
                    logger.info(f"Selected app type: {app_type}")
                except:
                    logger.warning(f"Could not select app type: {app_type}")
        except Exception as e:
            logger.warning(f"Could not select app types: {e}")

        time.sleep(1)

        # 10. What features did you use (checkboxes)
        try:
            features = [
                "Booking appointments",
                "Tracking physical activity or exercise",
                "Tracking diet or calories",
                "Health tips or education",
                "Online consultations",
                "Medicine reminders",
                "Period Tracking"
            ]
            # Select 2-3 random features
            num_features = random.randint(2, 3)
            selected_features = random.sample(features, num_features)

            for feature in selected_features:
                try:
                    checkbox = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{feature}')]/preceding-sibling::div//input[@type='checkbox']")
                    checkbox.click()
                    logger.info(f"Selected feature: {feature}")
                except:
                    logger.warning(f"Could not select feature: {feature}")
        except Exception as e:
            logger.warning(f"Could not select features: {e}")

        # 11. Did the app ask for personal information?
        try:
            personal_info_options = ["Yes", "No", "I don't remember"]
            selected_personal_info = random.choice(personal_info_options)
            personal_info_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_personal_info}') and ancestor::div[contains(@aria-label, 'personal information')]]/preceding-sibling::div//input[@type='radio']")
            personal_info_radio.click()
            logger.info(f"Personal information asked: {selected_personal_info}")
        except Exception as e:
            logger.warning(f"Could not select personal information option: {e}")

        # 12. Were you comfortable sharing that information?
        try:
            comfort_options = ["Yes", "No", "Maybe / Not Sure"]
            selected_comfort = random.choice(comfort_options)
            comfort_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_comfort}') and ancestor::div[contains(@aria-label, 'comfortable sharing')]]/preceding-sibling::div//input[@type='radio']")
            comfort_radio.click()
            logger.info(f"Comfort sharing: {selected_comfort}")
        except Exception as e:
            logger.warning(f"Could not select comfort option: {e}")

        # 13. Would you share data with research institutes?
        try:
            research_options = ["Yes", "No", "Maybe"]
            selected_research = random.choice(research_options)
            research_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_research}') and ancestor::div[contains(@aria-label, 'research institutes')]]/preceding-sibling::div//input[@type='radio']")
            research_radio.click()
            logger.info(f"Share with research: {selected_research}")
        except Exception as e:
            logger.warning(f"Could not select research sharing option: {e}")

        # 14. Do you trust health apps to keep information safe?
        try:
            trust_options = ["Yes", "No", "Maybe / Not sure"]
            selected_trust = random.choice(trust_options)
            trust_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_trust}') and ancestor::div[contains(@aria-label, 'trust health apps')]]/preceding-sibling::div//input[@type='radio']")
            trust_radio.click()
            logger.info(f"Trust health apps: {selected_trust}")
        except Exception as e:
            logger.warning(f"Could not select trust option: {e}")

        # 15. Should schools teach more about data privacy?
        try:
            privacy_education_options = ["Yes", "No", "Maybe"]
            selected_privacy_education = random.choice(privacy_education_options)
            privacy_radio = self.driver.find_element(By.XPATH, f"//span[contains(text(), '{selected_privacy_education}') and ancestor::div[contains(@aria-label, 'schools should teach')]]/preceding-sibling::div//input[@type='radio']")
            privacy_radio.click()
            logger.info(f"Privacy education: {selected_privacy_education}")
        except Exception as e:
            logger.warning(f"Could not select privacy education option: {e}")

        time.sleep(1)

    def submit_form(self):
        """Submit the form"""
        try:
            # Try different submit button selectors
            submit_selectors = [
                "//div[@role='button' and contains(., 'Submit')]",
                "//span[contains(text(), 'Submit')]/..",
                "//*[contains(text(), 'Submit')]",
                "//input[@type='submit']",
                "//button[contains(text(), 'Submit')]"
            ]

            for selector in submit_selectors:
                try:
                    submit_button = self.driver.find_element(By.XPATH, selector)
                    submit_button.click()
                    logger.info("Submit button clicked successfully")
                    return True
                except:
                    continue

            logger.error("Could not find submit button")
            return False

        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    def run_multiple_submissions(self, base_name, base_email, num_submissions, start_number=1):
        """
        Run multiple form submissions with incremental emails

        Args:
            base_name (str): Base name to use (e.g., "Parth Thapa")
            base_email (str): Base email without number (e.g., "parththapa")
            num_submissions (int): Number of submissions to make
            start_number (int): Starting number for email increment
        """
        # Handle Google login first
        if not self.handle_google_login():
            logger.error("Login failed. Aborting...")
            return

        successful_submissions = 0
        failed_submissions = 0
        
        for i in range(num_submissions):
            email_number = start_number + i
            email = f"{base_email}{email_number}@gmail.com"
            
            success = self.fill_form(base_name, email, i + 1)
            
            if success:
                successful_submissions += 1
                logger.info(f"✓ Submission {i + 1} completed successfully")
            else:
                failed_submissions += 1
                logger.error(f"✗ Submission {i + 1} failed")
            
            # Add delay between submissions to avoid being flagged
            if i < num_submissions - 1:  # Don't wait after the last submission
                time.sleep(3)
        
        logger.info(f"\n=== SUMMARY ===")
        logger.info(f"Total submissions attempted: {num_submissions}")
        logger.info(f"Successful submissions: {successful_submissions}")
        logger.info(f"Failed submissions: {failed_submissions}")
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def main():
    """Main function to run the form filler"""
    # Configuration
    FORM_URL = "https://forms.gle/2cHuJVWGHVi3cDhA8"
    NAME = "Parth Thapa"
    BASE_EMAIL = "parththapa"  # <NAME_EMAIL>, <EMAIL>, etc.
    NUM_SUBMISSIONS = 5  # Number of times to fill the form
    START_NUMBER = 1  # Starting number for email increment
    HEADLESS = False  # Set to True to run without opening browser window
    MANUAL_LOGIN = True  # Set to False to skip manual login

    # Create and run the form filler
    form_filler = GoogleFormFiller(FORM_URL, headless=HEADLESS, manual_login=MANUAL_LOGIN)
    
    try:
        form_filler.run_multiple_submissions(
            base_name=NAME,
            base_email=BASE_EMAIL,
            num_submissions=NUM_SUBMISSIONS,
            start_number=START_NUMBER
        )
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
    finally:
        form_filler.close()

if __name__ == "__main__":
    main()
