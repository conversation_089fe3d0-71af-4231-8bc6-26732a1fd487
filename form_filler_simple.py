#!/usr/bin/env python3
"""
Simple Google Form Auto-Filler Bot (without webdriver-manager)
Automatically fills a Google Form multiple times with incremental email addresses
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleFormFiller:
    def __init__(self, form_url, headless=False, manual_login=True):
        """
        Initialize the form filler bot

        Args:
            form_url (str): The Google Form URL
            headless (bool): Whether to run browser in headless mode
            manual_login (bool): Whether to wait for manual login
        """
        self.form_url = form_url
        self.driver = None
        self.wait = None
        self.manual_login = manual_login
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            # Simple approach - assumes ChromeDriver is in PATH or same directory
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            logger.error("Please ensure ChromeDriver is installed and in your PATH")
            logger.error("Download from: https://chromedriver.chromium.org/")
            raise

    def handle_google_login(self):
        """Handle Google login process"""
        if not self.manual_login:
            return True

        try:
            logger.info("Opening Google login page...")
            self.driver.get("https://accounts.google.com/signin")

            logger.info("=" * 60)
            logger.info("PLEASE SIGN IN TO YOUR GOOGLE ACCOUNT")
            logger.info("=" * 60)
            logger.info("1. The browser window should now be open")
            logger.info("2. Please sign in to your Google account")
            logger.info("3. Complete any 2FA if required")
            logger.info("4. Once signed in, press ENTER here to continue...")
            logger.info("=" * 60)

            # Wait for user to complete login
            input("Press ENTER after you have successfully signed in to Google: ")

            # Verify login by checking if we can access Google services
            logger.info("Verifying login status...")
            self.driver.get("https://myaccount.google.com/")
            time.sleep(3)

            # Check if we're logged in (look for account elements)
            try:
                # Look for elements that indicate successful login
                self.wait.until(EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-ogsr-up]")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[aria-label*='Google Account']")),
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".gb_A"))
                ))
                logger.info("✓ Google login verified successfully!")
                return True
            except TimeoutException:
                logger.warning("Could not verify login status, but continuing anyway...")
                return True

        except Exception as e:
            logger.error(f"Error during login process: {e}")
            logger.info("Continuing anyway - the form might still work...")
            return True
    
    def fill_form(self, name, email, submission_number):
        """
        Fill the Google Form with provided data
        
        Args:
            name (str): Name to fill in the form
            email (str): Email to fill in the form
            submission_number (int): Current submission number for logging
        """
        try:
            logger.info(f"Submission #{submission_number}: Filling form for {email}")
            
            # Navigate to the form
            self.driver.get(self.form_url)
            time.sleep(3)  # Wait for page to load
            
            # Wait for form to be ready
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))
            
            # Find all input fields
            input_fields = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[type='email']")
            
            if len(input_fields) >= 2:
                # Assume first field is name, second is email
                name_field = input_fields[0]
                email_field = input_fields[1]
                
                # Fill name field
                name_field.clear()
                name_field.send_keys(name)
                logger.info(f"Name field filled with: {name}")
                
                # Fill email field
                email_field.clear()
                email_field.send_keys(email)
                logger.info(f"Email field filled with: {email}")
                
            else:
                # Try alternative selectors
                try:
                    # Try to find by aria-label or placeholder
                    name_field = self.driver.find_element(By.XPATH, "//input[contains(@aria-label, 'Name') or contains(@aria-label, 'name')]")
                    name_field.clear()
                    name_field.send_keys(name)
                    logger.info(f"Name field found and filled with: {name}")
                except:
                    logger.warning("Could not find name field")
                
                try:
                    email_field = self.driver.find_element(By.XPATH, "//input[contains(@aria-label, 'Email') or contains(@aria-label, 'email') or @type='email']")
                    email_field.clear()
                    email_field.send_keys(email)
                    logger.info(f"Email field found and filled with: {email}")
                except:
                    logger.warning("Could not find email field")
            
            # Wait a moment before submitting
            time.sleep(2)
            
            # Find and click submit button
            try:
                # Try different submit button selectors
                submit_button = None
                
                # Method 1: Look for submit button by role and text
                try:
                    submit_button = self.driver.find_element(By.XPATH, "//div[@role='button' and contains(., 'Submit')]")
                except:
                    pass
                
                # Method 2: Look for submit span
                if not submit_button:
                    try:
                        submit_button = self.driver.find_element(By.XPATH, "//span[contains(text(), 'Submit')]/..")
                    except:
                        pass
                
                # Method 3: Look for any button with submit text
                if not submit_button:
                    try:
                        submit_button = self.driver.find_element(By.XPATH, "//*[contains(text(), 'Submit')]")
                    except:
                        pass
                
                if submit_button:
                    submit_button.click()
                    logger.info(f"Form submitted successfully for {email}")
                    
                    # Wait for submission confirmation
                    time.sleep(3)
                    return True
                else:
                    logger.error("Could not find submit button")
                    return False
                    
            except Exception as e:
                logger.error(f"Error clicking submit button: {e}")
                return False
            
        except TimeoutException:
            logger.error(f"Timeout while filling form for {email}")
            return False
        except Exception as e:
            logger.error(f"Error filling form for {email}: {e}")
            return False
    
    def run_multiple_submissions(self, base_name, base_email, num_submissions, start_number=1):
        """
        Run multiple form submissions with incremental emails

        Args:
            base_name (str): Base name to use (e.g., "Parth Thapa")
            base_email (str): Base email without number (e.g., "parththapa")
            num_submissions (int): Number of submissions to make
            start_number (int): Starting number for email increment
        """
        # Handle Google login first
        if not self.handle_google_login():
            logger.error("Login failed. Aborting...")
            return

        successful_submissions = 0
        failed_submissions = 0
        
        for i in range(num_submissions):
            email_number = start_number + i
            email = f"{base_email}{email_number}@gmail.com"
            
            success = self.fill_form(base_name, email, i + 1)
            
            if success:
                successful_submissions += 1
                logger.info(f"✓ Submission {i + 1} completed successfully")
            else:
                failed_submissions += 1
                logger.error(f"✗ Submission {i + 1} failed")
            
            # Add delay between submissions to avoid being flagged
            if i < num_submissions - 1:  # Don't wait after the last submission
                time.sleep(3)
        
        logger.info(f"\n=== SUMMARY ===")
        logger.info(f"Total submissions attempted: {num_submissions}")
        logger.info(f"Successful submissions: {successful_submissions}")
        logger.info(f"Failed submissions: {failed_submissions}")
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def main():
    """Main function to run the form filler"""
    # Configuration
    FORM_URL = "https://forms.gle/2cHuJVWGHVi3cDhA8"
    NAME = "Parth Thapa"
    BASE_EMAIL = "parththapa"  # <NAME_EMAIL>, <EMAIL>, etc.
    NUM_SUBMISSIONS = 5  # Number of times to fill the form
    START_NUMBER = 1  # Starting number for email increment
    HEADLESS = False  # Set to True to run without opening browser window
    MANUAL_LOGIN = True  # Set to False to skip manual login

    # Create and run the form filler
    form_filler = GoogleFormFiller(FORM_URL, headless=HEADLESS, manual_login=MANUAL_LOGIN)
    
    try:
        form_filler.run_multiple_submissions(
            base_name=NAME,
            base_email=BASE_EMAIL,
            num_submissions=NUM_SUBMISSIONS,
            start_number=START_NUMBER
        )
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
    finally:
        form_filler.close()

if __name__ == "__main__":
    main()
