#!/usr/bin/env python3
"""
Setup script for Google Form Auto-Filler Bot
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def check_chrome():
    """Check if Chrome is installed"""
    print("Checking for Google Chrome...")
    
    # Common Chrome installation paths
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✓ Chrome found at: {path}")
            return True
    
    print("⚠ Chrome not found in common locations.")
    print("Please make sure Google Chrome is installed on your system.")
    return False

def main():
    """Main setup function"""
    print("=== Google Form Auto-Filler Bot Setup ===\n")
    
    # Check Chrome installation
    chrome_ok = check_chrome()
    
    # Install requirements
    requirements_ok = install_requirements()
    
    print("\n=== Setup Summary ===")
    if chrome_ok and requirements_ok:
        print("✓ Setup completed successfully!")
        print("\nYou can now run the bot with:")
        print("python form_filler_bot.py")
    else:
        print("✗ Setup encountered issues:")
        if not chrome_ok:
            print("  - Chrome browser not found")
        if not requirements_ok:
            print("  - Failed to install Python requirements")
        print("\nPlease resolve these issues before running the bot.")

if __name__ == "__main__":
    main()
